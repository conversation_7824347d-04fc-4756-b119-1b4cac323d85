const StringEncoder = require('./stringEncoder');
const VariableRenamer = require('./variableRenamer');
const FunctionRenamer = require('./functionRenamer');
const NumberObfuscator = require('./numberObfuscator');

class LuaObfuscator {
    constructor() {
        this.stringEncoder = new StringEncoder();
        this.variableRenamer = new VariableRenamer();
        this.functionRenamer = new FunctionRenamer();
        this.numberObfuscator = new NumberObfuscator();
    }

    // Main obfuscation function
    obfuscate(code, options = {}) {
        const {
            stringEncoding = true,
            stringMethod = 'hex',
            variableRenaming = true,
            functionRenaming = true,
            numberObfuscation = true,
            numberMethod = 'arithmetic',
            mixedNumberMethods = false
        } = options;

        let result = code;
        const stats = {
            originalSize: code.length,
            processedFeatures: [],
            variableMap: {},
            functionMap: {},
            errors: []
        };

        try {
            // Step 1: String encoding
            if (stringEncoding) {
                console.log('Applying string encoding...');
                result = this.stringEncoder.processLuaCode(result, stringMethod);
                stats.processedFeatures.push(`String Encoding (${stringMethod})`);
            }

            // Step 2: Function renaming (before variable renaming to avoid conflicts)
            if (functionRenaming) {
                console.log('Applying function renaming...');
                result = this.functionRenamer.renameFunctions(result);
                stats.functionMap = this.functionRenamer.getFunctionMap();
                stats.processedFeatures.push('Function Renaming');
            }

            // Step 3: Variable renaming
            if (variableRenaming) {
                console.log('Applying variable renaming...');
                result = this.variableRenamer.renameVariables(result);
                stats.variableMap = this.variableRenamer.getVariableMap();
                stats.processedFeatures.push('Variable Renaming');
            }

            // Step 4: Number obfuscation
            if (numberObfuscation) {
                console.log('Applying number obfuscation...');
                if (mixedNumberMethods) {
                    result = this.numberObfuscator.processLuaCodeMixed(result);
                    stats.processedFeatures.push('Number Obfuscation (Mixed Methods)');
                } else {
                    result = this.numberObfuscator.processLuaCode(result, numberMethod);
                    stats.processedFeatures.push(`Number Obfuscation (${numberMethod})`);
                }
            }

            stats.finalSize = result.length;
            stats.compressionRatio = ((stats.finalSize - stats.originalSize) / stats.originalSize * 100).toFixed(2);

        } catch (error) {
            stats.errors.push(error.message);
            console.error('Obfuscation error:', error);
        }

        return {
            code: result,
            stats: stats
        };
    }

    // Quick obfuscation with default settings
    quickObfuscate(code) {
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'hex',
            variableRenaming: true,
            functionRenaming: true,
            numberObfuscation: true,
            numberMethod: 'arithmetic'
        });
    }

    // Heavy obfuscation with all features enabled
    heavyObfuscate(code) {
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'xor',
            variableRenaming: true,
            functionRenaming: true,
            numberObfuscation: true,
            mixedNumberMethods: true
        });
    }

    // Light obfuscation with minimal changes
    lightObfuscate(code) {
        return this.obfuscate(code, {
            stringEncoding: true,
            stringMethod: 'reverse',
            variableRenaming: false,
            functionRenaming: false,
            numberObfuscation: true,
            numberMethod: 'hex'
        });
    }

    // Get available encoding methods
    getAvailableMethods() {
        return {
            stringMethods: ['hex', 'base64', 'unicode', 'reverse', 'caesar', 'xor'],
            numberMethods: ['arithmetic', 'bitwise', 'hex', 'expression', 'table', 'function'],
            presets: ['quick', 'heavy', 'light', 'custom']
        };
    }

    // Validate Lua code syntax (basic check)
    validateLuaCode(code) {
        const errors = [];
        
        // Check for balanced parentheses
        let parenCount = 0;
        let braceCount = 0;
        let bracketCount = 0;
        
        for (let i = 0; i < code.length; i++) {
            const char = code[i];
            switch (char) {
                case '(':
                    parenCount++;
                    break;
                case ')':
                    parenCount--;
                    break;
                case '{':
                    braceCount++;
                    break;
                case '}':
                    braceCount--;
                    break;
                case '[':
                    bracketCount++;
                    break;
                case ']':
                    bracketCount--;
                    break;
            }
        }
        
        if (parenCount !== 0) errors.push('Unbalanced parentheses');
        if (braceCount !== 0) errors.push('Unbalanced braces');
        if (bracketCount !== 0) errors.push('Unbalanced brackets');
        
        // Check for basic Lua syntax patterns
        const requiredPatterns = [
            { pattern: /\bfunction\b/, message: 'No function declarations found' },
        ];
        
        // Check for common syntax errors
        const errorPatterns = [
            { pattern: /\bfunction\s*\(/g, message: 'Invalid function syntax' },
            { pattern: /\bend\s*\(/g, message: 'Invalid end statement' }
        ];
        
        errorPatterns.forEach(({ pattern, message }) => {
            if (pattern.test(code)) {
                errors.push(message);
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
}

module.exports = LuaObfuscator;
