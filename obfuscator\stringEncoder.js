class StringEncoder {
    constructor() {
        this.encodingMethods = {
            base64: this.base64Encode.bind(this),
            hex: this.hexEncode.bind(this),
            unicode: this.unicodeEncode.bind(this),
            reverse: this.reverseEncode.bind(this),
            caesar: this.caesarEncode.bind(this),
            custom: this.customEncode.bind(this)
        };
    }

    // Base64 encoding
    base64Encode(str) {
        const encoded = Buffer.from(str).toString('base64');
        return `(function() local b='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/' local function decode(data) local result = '' local pad = string.sub(data, -2) == '==' and 2 or string.sub(data, -1) == '=' and 1 or 0 for i = 1, #data - pad, 4 do local a, b, c, d = string.byte(data, i, i + 3) a = string.find(b, string.char(a)) - 1 b = string.find(b, string.char(b)) - 1 c = string.find(b, string.char(c)) - 1 d = string.find(b, string.char(d)) - 1 result = result .. string.char(bit32.bor(bit32.lshift(a, 2), bit32.rshift(b, 4))) if c then result = result .. string.char(bit32.bor(bit32.lshift(bit32.band(b, 15), 4), bit32.rshift(c, 2))) end if d then result = result .. string.char(bit32.bor(bit32.lshift(bit32.band(c, 3), 6), d)) end end return result end return decode('${encoded}') end)()`;
    }

    // Hexadecimal encoding
    hexEncode(str) {
        const encoded = Buffer.from(str).toString('hex');
        return `(function() local hex = '${encoded}' local result = '' for i = 1, #hex, 2 do result = result .. string.char(tonumber(string.sub(hex, i, i + 1), 16)) end return result end)()`;
    }

    // Unicode encoding
    unicodeEncode(str) {
        const encoded = str.split('').map(char => char.charCodeAt(0)).join(',');
        return `(function() local codes = {${encoded}} local result = '' for i = 1, #codes do result = result .. string.char(codes[i]) end return result end)()`;
    }

    // Reverse encoding
    reverseEncode(str) {
        const reversed = str.split('').reverse().join('');
        return `(function() local rev = '${reversed}' return string.reverse(rev) end)()`;
    }

    // Caesar cipher encoding
    caesarEncode(str, shift = 3) {
        const encoded = str.split('').map(char => {
            const code = char.charCodeAt(0);
            if (code >= 65 && code <= 90) {
                return String.fromCharCode(((code - 65 + shift) % 26) + 65);
            } else if (code >= 97 && code <= 122) {
                return String.fromCharCode(((code - 97 + shift) % 26) + 97);
            }
            return char;
        }).join('');
        
        return `(function() local encoded = '${encoded}' local shift = ${shift} local result = '' for i = 1, #encoded do local char = string.sub(encoded, i, i) local code = string.byte(char) if code >= 65 and code <= 90 then result = result .. string.char(((code - 65 - shift) % 26) + 65) elseif code >= 97 and code <= 122 then result = result .. string.char(((code - 97 - shift) % 26) + 97) else result = result .. char end end return result end)()`;
    }

    // Custom XOR encoding
    customEncode(str, key = 42) {
        const encoded = str.split('').map(char => char.charCodeAt(0) ^ key).join(',');
        return `(function() local encoded = {${encoded}} local key = ${key} local result = '' for i = 1, #encoded do result = result .. string.char(encoded[i] ~ key) end return result end)()`;
    }

    // Main encoding function
    encodeString(str, method = 'base64') {
        if (!this.encodingMethods[method]) {
            throw new Error(`Unknown encoding method: ${method}`);
        }
        return this.encodingMethods[method](str);
    }

    // Process Lua code to encode all strings
    processLuaCode(code, method = 'base64') {
        // Match string literals (both single and double quotes)
        const stringRegex = /(['"])((?:\\.|(?!\1)[^\\])*?)\1/g;
        
        return code.replace(stringRegex, (match, quote, content) => {
            // Skip if it's already encoded or contains special patterns
            if (content.includes('function()') || content.includes('return') || content.length < 2) {
                return match;
            }
            
            try {
                return this.encodeString(content, method);
            } catch (error) {
                console.warn(`Failed to encode string: ${content}`, error);
                return match;
            }
        });
    }
}

module.exports = StringEncoder;
