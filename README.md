# Lua Obfuscator - Professional Roblox Script Protection

A powerful web-based Lua obfuscator specifically designed for Roblox script protection. This tool provides multiple obfuscation techniques to protect your Lua scripts from reverse engineering and unauthorized access.

## 🚀 Features

### Core Obfuscation Tools

1. **String Encoding** - 6 different methods:
   - Hexadecimal encoding
   - Base64 encoding
   - Unicode encoding
   - Reverse encoding
   - Caesar cipher
   - XOR encoding

2. **Variable Renaming**
   - Automatically renames all local variables and function parameters
   - Generates random, unreadable variable names
   - Preserves Lua reserved words and built-in functions

3. **Function Renaming**
   - Renames user-defined functions with random names
   - Preserves built-in Lua functions and Roblox API methods
   - Handles local functions, global functions, and table methods
   - Maintains function call relationships

4. **Number Obfuscation** - 6 different methods:
   - Arithmetic operations (addition, subtraction, multiplication, division)
   - Bitwise operations (XOR, bit shifting)
   - Hexadecimal representation
   - Complex mathematical expressions
   - Table-based obfuscation
   - Function-based obfuscation

### Obfuscation Presets

- **Quick**: Fast obfuscation with basic protection
- **Heavy**: Maximum protection with all features enabled
- **Light**: Minimal obfuscation for testing purposes
- **Custom**: Configure your own obfuscation settings

## 🛠️ Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the server:
   ```bash
   npm start
   ```
4. Open your browser and navigate to `http://localhost:3000`

## 📖 Usage

### Web Interface

1. **Input Code**: Paste your Lua/Roblox script in the left editor
2. **Choose Preset**: Select Quick, Heavy, Light, or Custom obfuscation
3. **Configure Options** (Custom mode):
   - Enable/disable string encoding and choose method
   - Enable/disable variable renaming
   - Enable/disable function renaming
   - Enable/disable number obfuscation and choose method
4. **Obfuscate**: Click the obfuscation button
5. **Copy/Download**: Get your obfuscated code

### API Endpoints

#### Health Check
```
GET /api/health
```

#### Get Available Methods
```
GET /api/methods
```

#### Quick Obfuscation
```
POST /api/obfuscate/quick
Content-Type: application/json

{
  "code": "your lua code here"
}
```

#### Heavy Obfuscation
```
POST /api/obfuscate/heavy
Content-Type: application/json

{
  "code": "your lua code here"
}
```

#### Light Obfuscation
```
POST /api/obfuscate/light
Content-Type: application/json

{
  "code": "your lua code here"
}
```

#### Custom Obfuscation
```
POST /api/obfuscate
Content-Type: application/json

{
  "code": "your lua code here",
  "options": {
    "stringEncoding": true,
    "stringMethod": "hex",
    "variableRenaming": true,
    "functionRenaming": true,
    "numberObfuscation": true,
    "numberMethod": "arithmetic",
    "mixedNumberMethods": false
  }
}
```

#### Code Validation
```
POST /api/validate
Content-Type: application/json

{
  "code": "your lua code here"
}
```

## 🔧 Configuration Options

### String Encoding Methods
- `hex`: Hexadecimal encoding
- `base64`: Base64 encoding
- `unicode`: Unicode character codes
- `reverse`: String reversal
- `caesar`: Caesar cipher
- `xor`: XOR encoding

### Number Obfuscation Methods
- `arithmetic`: Mathematical operations
- `bitwise`: Bitwise operations
- `hex`: Hexadecimal representation
- `expression`: Complex expressions
- `table`: Table-based lookup
- `function`: Function-based generation

## 📊 Statistics

The obfuscator provides detailed statistics including:
- Original code size
- Final code size
- Size change percentage
- Applied obfuscation features
- Variable mapping (when variable renaming is enabled)
- Function mapping (when function renaming is enabled)

## 🧪 Testing

Run the API tests:
```bash
node test_api.js
```

## 📁 Project Structure

```
lua-obfuscator/
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
├── routes/
│   └── obfuscator.js      # API routes
├── obfuscator/
│   ├── luaObfuscator.js   # Main obfuscator class
│   ├── stringEncoder.js   # String encoding module
│   ├── variableRenamer.js # Variable renaming module
│   ├── functionRenamer.js # Function renaming module
│   └── numberObfuscator.js # Number obfuscation module
├── public/
│   ├── index.html         # Frontend interface
│   ├── styles.css         # Styling
│   └── script.js          # Frontend JavaScript
└── test_api.js           # API testing script
```

## ⚠️ Important Notes

- **Always keep backups** of your original code
- **Test obfuscated code** before deployment
- Heavy obfuscation may significantly increase script size
- Some obfuscation methods may impact performance
- This tool is designed for legitimate script protection

## 🔒 Security Features

- Input validation and sanitization
- File size limits (1MB max)
- Error handling and logging
- CORS protection
- Request rate limiting ready

## 🚀 Deployment

For production deployment:

1. Set environment variables:
   ```bash
   export PORT=3000
   export NODE_ENV=production
   ```

2. Use a process manager like PM2:
   ```bash
   npm install -g pm2
   pm2 start server.js --name "lua-obfuscator"
   ```

## 📄 License

MIT License - Feel free to use this tool for your projects.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

---

**Created for Roblox developers who need professional script protection.**
